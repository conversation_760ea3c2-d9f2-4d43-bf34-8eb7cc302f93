import os
import typing
from abc import abstractmethod, ABC

from sqlalchemy.engine import Row

from db import format_query_values
from indexer.processors import AbstractProcessor, KeepsMessage

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class AbstractEnrollmentProcessor(AbstractProcessor, ABC):
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'

    def __init__(self):
        super().__init__()

    @abstractmethod
    def get_kafka_main_topic(self) -> str:
        pass

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        return None

    @abstractmethod
    def get_select_sql_path(self) -> str:
        pass

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    def remove_deleted_instances(self, deleted_ids: typing.Set[str], *args):
        with self.db.regulatory_compliance.connect() as conn:
            deleted_ids = ','.join(f"'{item}'" for item in deleted_ids)
            self.db.run_sql(conn, 'regulatory_compliance/enrollments_delete.sql', ids=deleted_ids)

    @abstractmethod
    def _transform_row(self, pk: str, row: Row) -> dict:
        pass

    @abstractmethod
    def _save_related_entities(self, batch: typing.List[KeepsMessage]):
        pass

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:
        self._save_related_entities(batch)
        with self.db.konquest_engine.connect() as konquest_conn:
            enrollments = []

            in_ids = ','.join([f"'{id}'" for id in updated_ids])
            rs = self.db.run_sql(konquest_conn, self.get_select_sql_path(), enrollments_ids=in_ids)

            for i, row in enumerate(rs):
                enrollment_id = self.str_id(row['id'])
                self.log(f'[{enrollment_id}] -- Processing {i+1} of {len(updated_ids)}')
                enrollment = self._transform_row(enrollment_id, row)
                enrollments.append(enrollment)

        with self.db.regulatory_compliance.connect() as conn:
            for enrollment in enrollments:
                enrollment = format_query_values(enrollment)
                self.db.run_sql(conn, 'regulatory_compliance/enrollments_save.sql', **enrollment)

        return True

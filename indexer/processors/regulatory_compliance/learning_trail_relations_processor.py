import os
import typing

from indexer.processors import AbstractProcessor, KeepsMessage
from indexer.processors.regulatory_compliance.learning_trail_processor import LearningTrailProcessor

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class LearningTrailRelationsProcessor(AbstractProcessor):
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    LEARNING_TRAIL_WORKSPACE = 'learning_trail_workspace'

    def __init__(self, learning_trail_processor: LearningTrailProcessor):
        super().__init__()
        self._learning_trail_processor = learning_trail_processor

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            self.LEARNING_TRAIL_WORKSPACE,
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:
        updated_or_deleted_msgs = [msg for msg in batch if msg.op not in ['r']]

        trail_workspaces = [workspace for workspace in updated_or_deleted_msgs if workspace.topic == self.TOPIC_PREFIX + f'.{self.LEARNING_TRAIL_WORKSPACE}']

        self.log(f'-- {len(batch)} messages, {len(trail_workspaces)} learning-trail-workspace relations')
        affected_learning_trail_ids = self.extract_ids(
            trail_workspaces, {self.TOPIC_PREFIX + f'.{self.LEARNING_TRAIL_WORKSPACE}': lambda msg: msg.entity('learning_trail_id')}
        )
        if affected_learning_trail_ids:
            self._learning_trail_processor.do_process_batch([], affected_learning_trail_ids)
        with self.db.konquest_engine.connect() as konquest_conn,\
                self.db.regulatory_compliance.connect() as compliance_conn:
            for learning_trail_id in affected_learning_trail_ids:
                trail_workspaces = self._query_learning_trail_workspaces(konquest_conn, learning_trail_id)
                print(f'Updating workspaces for learning trail "{learning_trail_id}" with {len(trail_workspaces)} workspaces')
                self.save_workspace_relation(compliance_conn, learning_trail_id, trail_workspaces)
        return True

    def remove_deleted_instances(self, deleted_ids: typing.Set[str], delete_msgs: typing.List[KeepsMessage] = []):
        with self.db.regulatory_compliance.connect() as compliance_conn:
            self.db.run_sql(
                compliance_conn,
                'regulatory_compliance/learning_object_workspaces_delete.sql',
                source_ids=deleted_ids
            )

    def save_workspace_relation(self, konquest_conn, learning_trail_id: str, learning_trail_workspaces: typing.List[typing.Dict]):
        for learning_trail_workspace in learning_trail_workspaces:
            values = self.db.format_query_values(
                {
                    'source_id': learning_trail_workspace['id'],
                    'learning_object_source_id': learning_trail_id,
                    'workspace_id': learning_trail_workspace['workspace_id'],
                    'deleted_date': learning_trail_workspace['deleted_date']
                }
            )
            self.db.run_sql(
                konquest_conn,
                'regulatory_compliance/learning_object_workspaces_save.sql',
                **values
            )

    def _query_learning_trail_workspaces(self, konquest_conn, learning_trail_id) -> typing.List[typing.Dict]:
        workspaces = []
        rs = self.db.run_sql(
            konquest_conn,
            'konquest/all_learning_trail_workspaces.sql',
            learning_trail_id=learning_trail_id
        )
        for row in rs:
            workspaces.append({
                'id': self.str_id(row['id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
                'deleted_date': self.str_date(row['deleted_date']),
                'workspace_id': self.str_id(row['workspace_id']),
            })
        return workspaces

import os
import typing

from indexer.processors import AbstractProcessor, KeepsMessage
from indexer.processors.regulatory_compliance.mission_processor import MissionProcessor

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')


class MissionRelationsProcessor(AbstractProcessor):
    TOPIC_PREFIX = f'pg_konquest_{ENV_SUFFIX}.public'
    MISSION_WORKSPACE = 'mission_workspace'

    def __init__(self, mission_processor: MissionProcessor):
        super().__init__()
        self._mission_processor = mission_processor

    def get_kafka_main_topic(self) -> str:
        return None

    def get_kafka_sub_topics(self) -> typing.Dict[str, typing.Callable[[KeepsMessage], str]]:
        tables = [
            self.MISSION_WORKSPACE,
        ]
        topics = {f'{self.TOPIC_PREFIX}.{table}': None for table in tables}
        return topics

    def get_index_name(self) -> str:
        return None

    def get_index_mappings(self) -> str:
        return None

    def pre_process_batch(self, batch: typing.List[KeepsMessage]):
        return self.do_process_batch(batch, None)

    def do_process_batch(self, batch: typing.List[KeepsMessage], updated_ids: typing.Set[str]) -> bool:
        updated_or_deleted_msgs = [msg for msg in batch if msg.op not in ['r']]
        mission_workspaces = [
            workspace for workspace in updated_or_deleted_msgs if workspace.topic == self.TOPIC_PREFIX + f'.{self.MISSION_WORKSPACE}'
        ]
        self.log(f'-- {len(batch)} messages, {len(mission_workspaces)} mission-workspace relations')

        affected_missions_ids = self.extract_ids(
            mission_workspaces, {self.TOPIC_PREFIX + f'.{self.MISSION_WORKSPACE}': lambda msg: msg.entity('mission_id')}
        )
        if affected_missions_ids:
            self._mission_processor.do_process_batch([], affected_missions_ids)
        with self.db.konquest_engine.connect() as konquest_conn,\
                self.db.regulatory_compliance.connect() as compliance_conn:
            for mission_id in affected_missions_ids:
                mission_workspaces = self._query_course_workspaces(konquest_conn, mission_id)
                print(f'Updating workspaces for mission "{mission_id}" with {len(mission_workspaces)} workspaces')
                self.save_mission_workspace_relation(compliance_conn, mission_id, mission_workspaces)

        return True

    def remove_deleted_instances(self, deleted_ids: typing.Set[str], *args):
        with self.db.regulatory_compliance.connect() as compliance_conn:
            self.db.run_sql(
                compliance_conn,
                'regulatory_compliance/learning_object_workspaces_delete.sql',
                source_ids=deleted_ids
            )

    def save_mission_workspace_relation(self, konquest_conn, mission_id: str, mission_workspaces: typing.List[typing.Dict]):
        for mission_workspace in mission_workspaces:
            values = self.db.format_query_values(
                {
                    'source_id': mission_workspace['id'],
                    'learning_object_source_id': mission_id,
                    'workspace_id': mission_workspace['workspace_id'],
                    'deleted_date': mission_workspace['deleted_date']
                }
            )
            self.db.run_sql(
                konquest_conn,
                'regulatory_compliance/learning_object_workspaces_save.sql',
                **values
            )

    def _query_course_workspaces(self, konquest_conn, mission_id) -> typing.List[typing.Dict]:
        workspaces = []

        rs = self.db.run_sql(konquest_conn, 'konquest/all_mission_workspaces.sql', mission_id=mission_id)
        for row in rs:
            workspaces.append({
                'id': self.str_id(row['id']),
                'created_date': self.str_date(row['created_date']),
                'updated_date': self.str_date(row['updated_date']),
                'deleted_date': self.str_date(row['deleted_date']),
                'workspace_id': self.str_id(row['workspace_id'])
            })
        return workspaces

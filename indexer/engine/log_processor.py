import typing  # python 3.8<

from .base_processor import BaseProcessor
from .keeps_message import KeepsMessage


class LogProcessor(BaseProcessor):
    def __init__(self, topics: typing.List[str] = [], logMessages: bool = True) -> None:
        self.topics = topics
        self.logMessages = logMessages

    def get_kafka_topics(self) -> typing.List[str]:
        return self.topics

    def process_batch(self, batch: typing.List[KeepsMessage]) -> bool:
        print('[LOG] -----------------------')
        print('[LOG] batch size:', len(batch))

        if self.logMessages:
            for i, msg in enumerate(batch, start=1):
                print("")
                print('[LOG] message:', i)
                msg.log()

        print("")
        return True

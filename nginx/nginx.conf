server {
    listen 80;
    server_name localhost;

    location = / {
        return 200 "OK";
        add_header Content-Type text/plain;
    }

    location /connect/ {
        proxy_pass http://kafka-connectors:8083/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        auth_basic "Access Restricted";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    location /kafdrop/ {
        proxy_pass http://kafdrop:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        auth_basic "Access Restricted";
        auth_basic_user_file /etc/nginx/conf.d/.htpasswd;
    }

    location / {
        return 444; # no response
    }
}

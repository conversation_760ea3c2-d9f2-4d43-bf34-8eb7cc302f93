services: 
  smartzap-data-sync:
    build: .
    command: "python -u ./smartzap-app.py"
    environment:
      ENV_SUFFIX: 'dev'
      ELASTICSEARCH_SERVER: http://elastic:9200
      ELASTICSEARCH_PASSWORD: senhaSuperSegura
      ELASTICSEARCH_USER: elastic
      KAFKA_SERVERS: kafka:9092
      INDEXER_POLL_TIMEOUT: 5
      INDEXER_BATCH_MAX_SECONDS: 15
      INDEXER_BATCH_MAX_SIZE: 10
      INDEXER_LOG_MESSAGE: 'true'
      INDEXER_LOG_PAYLOAD: 'true'
      INDEXER_LOG_BATCH: 'true'
      DATABASE_DEV_POSTGRES_URL: 'postgresql://dev-postgres'
      TRAIL_LEARNING_OBJECT_TYPE_ID: "d841e9d8-d669-4d88-9636-1072765d0738"
      MISSION_LEARNING_OBJECT_TYPE_ID: "798e50d7-8b97-4979-8728-4f9f1599bb05"
      PG_HOSTNAME: host.docker.internal
      PG_PORT: "5432"
      PG_USER: postgres
      PG_PASSWORD: 123456
      PG_DBNAME_MYACCOUNT: myaccount_dev_db
      PG_DBNAME_KONQUEST: konquest_dev_db
      PG_DBNAME_KONTENT: kontent_dev_db
      PG_DBNAME_SMARTZAP: smartzap_dev_db
      PG_DBNAME_NOTIFICATION: notification_dev_db
      PG_DBNAME_REGULATORY_COMPLIANCE: regulatory_compliance_dev_db
      PG_DBNAME_INTEGRATION_GATEWAY_ALURA: integration_gateway_alura_dev_db

  elastic:
    image: elasticsearch:7.14.2
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=senhaSuperSegura

  kibana:
    image: kibana:7.14.2
    ports:
      - 5601:5601
    depends_on:
      - elastic
    environment:
      ELASTICSEARCH_HOSTS: http://elastic:9200

  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
    ports:
      - "2181:2181"

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  control-center:
    image: confluentinc/cp-enterprise-control-center:7.5.0
    depends_on:
      - kafka
    ports:
      - "9021:9021"
    environment:
      CONTROL_CENTER_BOOTSTRAP_SERVERS: kafka:9092
      CONTROL_CENTER_ZOOKEEPER_CONNECT: zookeeper:2181
      CONTROL_CENTER_REPLICATION_FACTOR: 1
      CONTROL_CENTER_CONNECT_CLUSTER: kafka:9092
      CONFLUENT_METRICS_ENABLE: 'false'

networks:
  default:
    name: kafka-connectors_net
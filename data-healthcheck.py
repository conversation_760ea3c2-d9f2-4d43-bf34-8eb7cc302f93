import itertools
import os
from typing import Any, List, Iterator
from sqlalchemy.engine import Connection
from indexer import processors
from db import konquest_engine, myaccount_engine
from es import es_client

ENV_SUFFIX = os.getenv('ENV_SUFFIX', 'dev')
DIFF_THRESHOLD = 0.01
ALWAYS_SHOW_DIFF_IDS = False


def diff_count(index: str, table: str, db_conn: Connection, filter_logical_deleted=False) -> int:
    es_res = es_client.count(index=index)
    es_count = es_res['count']
    print(f'- es_count: {es_count}')

    sql = f'SELECT COUNT(*) FROM "{table}"'
    if filter_logical_deleted:
        sql = f'{sql} WHERE deleted = FALSE'

    db_res = db_conn.execute(sql)
    db_count = db_res.scalar()
    print(f'- db_count: {db_count}')

    diff = abs(db_count - es_count)
    max_count = max(db_count, es_count)
    status = '==='
    if diff > (max_count * DIFF_THRESHOLD):
        status = '!!!'
    elif diff > 0:
        status = '~~~'

    ratio = diff / max_count if max_count else 100
    print(f'- count diff: {diff} {status} {"%.4f %%" % (ratio * 100)}', )
    return diff


# Can be VERY slow
def diff_ids(index: str, table: str, db_conn: Connection):
    es_ids = _elastic_retrieve_ids(index)
    db_ids = _db_retrieve_ids(table, db_conn)

    # assume the ids are sorted

    matched = []
    es_only = []
    db_only = []

    es_pos = 0
    db_pos = 0
    while es_pos < len(es_ids) and db_pos < len(db_ids):
        if es_ids[es_pos] == db_ids[db_pos]:
            matched.append(es_ids[es_pos])
            es_pos += 1
            db_pos += 1
            continue
        if es_ids[es_pos] < db_ids[db_pos]:
            es_only.append(es_ids[es_pos])
            es_pos += 1
            continue
        # >
        db_only.append(db_ids[db_pos])
        db_pos += 1

    # handle last ids not matched
    es_only.extend(es_ids[es_pos:])
    db_only.extend(db_ids[db_pos:])

    print(f'- ids matched: {len(matched)}')
    print(f'- es only: {len(es_only)}')
    print(f'- db only: {len(db_only)}')
    return matched, es_only, db_only


def _elastic_retrieve_ids(index: str) -> List[str]:
    # create a point in time for continuous search on the index
    pit = es_client.open_point_in_time(index=index, keep_alive='1m')

    return list(itertools.chain([hits for hits in _elastic_retrieve_ids_iteration(pit)]))


def _elastic_retrieve_ids_iteration(pit, search_after=None) -> Iterator[List[Any]]:
    response = es_client.search(_source="id", sort="id", size=10000, pit=pit.body, search_after=search_after)
    hits = response.body['hits']['hits']

    if hits:
        yield from [doc['_source']['id'] for doc in hits]

        last_hit = hits[-1]
        search_after = last_hit['sort']
        # print(f'[ESF] _search_iteration - total_hits: {len(hits)} - last_hit: {last_hit}')
        yield from _elastic_retrieve_ids_iteration(pit, search_after)


def _db_retrieve_ids(table: str, db_conn: Connection) -> None:
    db_res = db_conn.execute(f'SELECT id FROM "{table}" ORDER BY id')
    rows = db_res.fetchall()
    return [str(row['id']) for row in rows]


if __name__ == '__main__':
    processorsList: List[processors.AbstractProcessor] = [
        processors.CourseProcessor(),
        processors.UserProcessor(),
        processors.EnrollmentProcessor(),
        processors.ActivityProcessor(),
        processors.AnswerProcessor(),
        processors.PulseProcessor(),
        processors.ChannelProcessor(),
        processors.CourseEvaluationsProcessor(),
        processors.CourseRatingsProcessor(),
        processors.CourseBookmarksProcessor(),
        processors.GroupProcessor(),
        processors.LearningTrailProcessor(),
        processors.LearningTrailEnrollmentProcessor(),

        # processors.AnswerRelationsProcessor(),
        # processors.ChannelRelationsProcessor(),
        # processors.CourseRelationsProcessor(),
        # processors.EnrollmentRelationsProcessor(),
    ]

    with konquest_engine.connect() as konquest_conn,\
         myaccount_engine.connect() as myaccount_conn:

        for proc in processorsList:
            index = proc.get_index_name()
            main_topic = proc.get_kafka_main_topic()
            main_topic_parts = main_topic.split('.') if main_topic else ['-', '-', '-']
            main_db_name = main_topic_parts[0]
            main_table_name = main_topic_parts[2]

            print('\n-----------------------')
            print(f'{type(proc).__name__}:')
            print(f'- index: {index}')
            print(f'- main_topic: {main_topic}')
            print(f'- main_db_name: {main_db_name}')
            print(f'- main_table_name: {main_table_name}')

            db_conn = myaccount_conn if 'myaccount' in main_db_name else konquest_conn
            diff = diff_count(index, main_table_name, db_conn, 'konquest' in main_db_name)

            if diff and (os.getenv('ENV_SUFFIX') in ['dev', 'stage'] or ALWAYS_SHOW_DIFF_IDS):
                matches, es_only, db_only = diff_ids(index, main_table_name, db_conn)
                print('------ ES ID LIST ------')
                print(es_only)

                print('------ DB ID LIST ------')
                print(db_only)

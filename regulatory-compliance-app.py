import os
import socket
import elasticapm

from indexer import IndexerWorker, processors

# See: https://docs.confluent.io/platform/current/clients/confluent-kafka-python/html/index.html#pythonclient-configuration
# Also: https://github.com/edenhill/librdkafka/blob/master/CONFIGURATION.md
kafka_config = {
    "client.id": socket.gethostname(),
    "group.id": os.getenv('INDEXER_REGULATORY_COMPLIANCE_GROUP_ID', 'regulatory-complaince-dev-consumer'),
    "bootstrap.servers": os.getenv('KAFKA_SERVERS', 'dev-broker:29092'),
    "auto.offset.reset": "earliest",
    "enable.auto.commit": False,  # commit in batch
    # See https://stackoverflow.com/questions/58517125/kafka-offset-management-enable-auto-commit-vs-enable-auto-offset-store
    # "enable.auto.offset.store": False,
}


apm_client: elasticapm.Client

if __name__ == '__main__':
    apm_client = elasticapm.Client(
        service_name=os.getenv('ELASTIC_APM_SERVICE_NAME', 'indexer'),
        server_url=os.getenv('ELASTIC_APM_SERVER_URL', 'https://keeps.apm.us-east-1.aws.cloud.es.io'),
        secret_token=os.getenv("ELASTIC_APM_SECRET_TOKEN"),
        environment=os.getenv("ELASTIC_APM_ENVIRONMENT", "development"),

        # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
        transaction_sample_rate=0.1,  # default: 1.0
        span_stack_trace_min_duration=-1,  # default: 5ms
        span_compression_same_kind_max_duration="5ms",  # default: 0ms,
    )
    elasticapm.instrument()

    indexer = IndexerWorker(kafka_config, apm_client=apm_client)
    trail_processor = processors.RegulatoryTrailProcessor()
    mission_processor = processors.RegulatoryMissionProcessor()

    indexer.addProcessor(trail_processor)
    indexer.addProcessor(mission_processor)
    indexer.addProcessor(processors.RegulatoryTrailRelationsProcessor(trail_processor))
    indexer.addProcessor(processors.RegulatoryMissionRelationsProcessor(mission_processor))
    indexer.addProcessor(processors.RegulatoryMissionEnrollmentProcessor(mission_processor))
    indexer.addProcessor(processors.RegulatoryTrailEnrollmentProcessor(trail_processor))

    try:
        indexer.start()
    except Exception as e:
        apm_client.capture_exception(exc_info=True)
        raise e

version: '3'

services: 

  kafka-data-indexer:
    image: ************.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/data-indexer:production
    environment:
      ENV_SUFFIX: ${ENV_SUFFIX:-production}
      KAFKA_SERVERS: ${CONNECT_BOOTSTRAP_SERVERS:?err}
      ELASTICSEARCH_SERVER: ${ELASTICSEARCH_SERVER:?err}
      ELASTICSEARCH_USER: ${ELASTICSEARCH_USER:-elastic}
      ELASTICSEARCH_PASSWORD: ${ELASTICSEARCH_PASSWORD}
      INDEXER_GROUP_ID: ${INDEXER_GROUP_ID:-keeps-indexer-production}
      INDEXER_POLL_TIMEOUT: ${INDEXER_POLL_TIMEOUT:-1}
      INDEXER_BATCH_MAX_SECONDS: ${INDEXER_BATCH_MAX_SECONDS:-60}
      INDEXER_BATCH_MAX_SIZE: ${INDEXER_BATCH_MAX_SIZE:-100}
      INDEXER_INIT_INDEXES: ${INDEXER_INIT_INDEXES:-true}
      INDEXER_INIT_INDEXES_ONLY: ${INDEXER_INIT_INDEXES_ONLY}
      INDEXER_LOG_MESSAGE: ${INDEXER_LOG_MESSAGE}
      INDEXER_LOG_PAYLOAD: ${INDEXER_LOG_PAYLOAD}
      INDEXER_LOG_BATCH: ${INDEXER_LOG_BATCH}
      ELASTIC_APM_ENVIRONMENT: ${ELASTIC_APM_ENVIRONMENT:-production}
      ELASTIC_APM_SECRET_TOKEN: ${ELASTIC_APM_SECRET_TOKEN:?err}
      # Postgres
      PG_HOSTNAME: ${PG_HOSTNAME:?err}
      PG_PORT: ${PG_PORT:?err}
      PG_USER: ${PG_USER:?err}
      PG_PASSWORD: ${PG_PASSWORD:?err}
      PG_DBNAME_MYACCOUNT: ${PG_DBNAME_MYACCOUNT:?err}
      PG_DBNAME_KONQUEST: ${PG_DBNAME_KONQUEST:?err}
      PG_DBNAME_KONTENT: ${PG_DBNAME_KONTENT:?err}


  kafka-connectors:
    image: ************.dkr.ecr.us-east-1.amazonaws.com/learning-analytics/keeps-connector:production
    ports:
      - "8083:8083"
    environment:
      ENV_SUFFIX: ${ENV_SUFFIX:-production}
      CONNECTOR_VERSION: ${CONNECTOR_VERSION:-0}
      CONNECT_BOOTSTRAP_SERVERS: ${CONNECT_BOOTSTRAP_SERVERS:?err}
      CONNECT_REST_ADVERTISED_HOST_NAME: ${CONNECT_REST_ADVERTISED_HOST_NAME:-localhost}
      CONNECT_REST_PORT: ${CONNECT_REST_PORT:-8083}
      CONNECT_GROUP_ID: ${CONNECT_GROUP_ID:-keeps-connect-production}
      CONNECT_CONFIG_STORAGE_TOPIC: ${CONNECT_CONFIG_STORAGE_TOPIC:-_keeps-connect-configs-production}
      CONNECT_OFFSET_STORAGE_TOPIC: ${CONNECT_OFFSET_STORAGE_TOPIC:-_keeps-connect-offsets-production}
      CONNECT_STATUS_STORAGE_TOPIC: ${CONNECT_STATUS_STORAGE_TOPIC:-_keeps-connect-status-production}
      CONNECT_KEY_CONVERTER: ${CONNECT_KEY_CONVERTER:-org.apache.kafka.connect.storage.StringConverter}
      CONNECT_VALUE_CONVERTER: ${CONNECT_VALUE_CONVERTER:-org.apache.kafka.connect.json.JsonConverter}
      CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: ${CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE:-false}
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: ${CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE:-false}
      CONNECT_REPLICATION_FACTOR: ${CONNECT_REPLICATION_FACTOR:-1}
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: ${CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR:-1}
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: ${CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR:-1}
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: ${CONNECT_STATUS_STORAGE_REPLICATION_FACTOR:-1}
      # Postgres
      PG_HOSTNAME: ${PG_HOSTNAME:?err}
      PG_PORT: ${PG_PORT:?err}
      PG_USER: ${PG_USER:?err}
      PG_PASSWORD: ${PG_PASSWORD:?err}
      PG_DBNAME_MYACCOUNT: ${PG_DBNAME_MYACCOUNT:?err}
      PG_DBNAME_KONQUEST: ${PG_DBNAME_KONQUEST:?err}
      PG_DBNAME_KONTENT: ${PG_DBNAME_KONTENT:?err}

  kafdrop:
    image: obsidiandynamics/kafdrop
    ports:
      - "9000:9000"
    environment:
      KAFKA_BROKERCONNECT: ${CONNECT_BOOTSTRAP_SERVERS:?err}
      SERVER_SERVLET_CONTEXTPATH: /kafdrop/
      CMD_ARGS: --topic.deleteEnabled=false --topic.createEnabled=false

  nginx:
    image: nginx:1.21-alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx:/etc/nginx/conf.d
